<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FeeThreshold extends Model
{
    protected $fillable = [
        'name',
        'amount',
        'description',
        'type',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this threshold.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get only active thresholds.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get grade access thresholds.
     */
    public function scopeGradeAccess($query)
    {
        return $query->where('type', 'grade_access')->where('is_active', true);
    }

    /**
     * Check if student exceeds threshold for grade access.
     */
    public static function studentExceedsGradeAccessThreshold(int $studentId, int $semesterId): bool
    {
        $threshold = self::gradeAccess()->first();

        if (!$threshold) {
            return false; // No threshold set, allow access
        }

        $outstandingBalance = Payment::getOutstandingBalance($studentId, $semesterId);

        return $outstandingBalance > $threshold->amount;
    }

    /**
     * Get active grade access threshold amount.
     */
    public static function getGradeAccessThreshold(): ?float
    {
        return self::gradeAccess()->first()?->amount;
    }
}
