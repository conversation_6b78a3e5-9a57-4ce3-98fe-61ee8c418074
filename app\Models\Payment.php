<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $fillable = [
        'user_id',
        'semester_id',
        'amount',
        'type',
        'description',
        'status',
        'payment_method',
        'transaction_id',
        'reference_number',
        'payment_date',
        'due_date',
        'processed_by',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
        'due_date' => 'date',
    ];

    /**
     * Get the student this payment belongs to.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the semester this payment is for.
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    /**
     * Get the accountant who processed this payment.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope to get completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Check if payment is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && now()->toDateString() > $this->due_date->toDateString() && $this->status !== 'completed';
    }

    /**
     * Calculate student's outstanding balance for a semester.
     */
    public static function getOutstandingBalance(int $studentId, int $semesterId): float
    {
        $totalDue = self::where('user_id', $studentId)
            ->where('semester_id', $semesterId)
            ->whereIn('status', ['pending', 'failed'])
            ->sum('amount');

        $totalPaid = self::where('user_id', $studentId)
            ->where('semester_id', $semesterId)
            ->where('status', 'completed')
            ->sum('amount');

        return $totalDue - $totalPaid;
    }
}
