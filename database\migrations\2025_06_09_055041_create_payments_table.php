<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // student
            $table->foreignId('semester_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->enum('type', ['tuition', 'fees', 'penalty', 'other'])->default('tuition');
            $table->string('description')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            $table->string('payment_method')->nullable(); // cash, card, bank_transfer, etc.
            $table->string('transaction_id')->nullable()->unique();
            $table->string('reference_number')->nullable();
            $table->date('payment_date');
            $table->date('due_date')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null'); // accountant
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'semester_id']);
            $table->index(['status', 'payment_date']);
            $table->index(['due_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
