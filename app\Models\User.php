<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'student_id',
        'phone',
        'address',
        'date_of_birth',
        'profile_photo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
        ];
    }

    /**
     * Get the user's primary role name.
     */
    public function getPrimaryRoleAttribute(): ?string
    {
        return $this->roles->first()?->name;
    }

    /**
     * Check if user is a student.
     */
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }

    /**
     * Check if user is a lecturer.
     */
    public function isLecturer(): bool
    {
        return $this->hasRole('lecturer');
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is an accountant.
     */
    public function isAccountant(): bool
    {
        return $this->hasRole('accountant');
    }

    /**
     * Check if user is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('super_admin');
    }

    /**
     * Get enrollments for this user (if student).
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Get courses this user is enrolled in (if student).
     */
    public function enrolledCourses()
    {
        return $this->belongsToMany(Course::class, 'enrollments')
            ->withPivot(['status', 'enrolled_at', 'dropped_at'])
            ->withTimestamps();
    }

    /**
     * Get courses this user teaches (if lecturer).
     */
    public function taughtCourses()
    {
        return $this->hasMany(Course::class, 'lecturer_id');
    }

    /**
     * Get assignments created by this user (if lecturer).
     */
    public function createdAssignments()
    {
        return $this->hasMany(Assignment::class, 'created_by');
    }

    /**
     * Get submissions by this user (if student).
     */
    public function submissions()
    {
        return $this->hasMany(Submission::class);
    }

    /**
     * Get payments by this user (if student).
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get SGPA for a specific semester.
     */
    public function getSGPA(int $semesterId): float
    {
        return Grade::calculateSGPA($this->id, $semesterId);
    }

    /**
     * Get CGPA across all semesters.
     */
    public function getCGPA(): float
    {
        return Grade::calculateCGPA($this->id);
    }

    /**
     * Check if user can access grades (fee threshold check).
     */
    public function canAccessGrades(int $semesterId): bool
    {
        if (!$this->isStudent()) {
            return true; // Non-students can always access
        }

        return !FeeThreshold::studentExceedsGradeAccessThreshold($this->id, $semesterId);
    }

    /**
     * Get outstanding balance for a semester.
     */
    public function getOutstandingBalance(int $semesterId): float
    {
        return Payment::getOutstandingBalance($this->id, $semesterId);
    }
}
