<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed roles and permissions first
        $this->call(RolePermissionSeeder::class);

        // Create a super admin user
        $superAdmin = User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
        ]);
        $superAdmin->assignRole('super_admin');

        // Create test users for each role
        $student = User::factory()->create([
            'name' => 'John Student',
            'email' => '<EMAIL>',
            'student_id' => 'STU001',
        ]);
        $student->assignRole('student');

        $lecturer = User::factory()->create([
            'name' => 'Jane Lecturer',
            'email' => '<EMAIL>',
        ]);
        $lecturer->assignRole('lecturer');

        $admin = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);
        $admin->assignRole('admin');

        $accountant = User::factory()->create([
            'name' => 'Accountant User',
            'email' => '<EMAIL>',
        ]);
        $accountant->assignRole('accountant');

        // Seed academic data (semesters, courses, enrollments, etc.)
        $this->call(AcademicDataSeeder::class);
    }
}
