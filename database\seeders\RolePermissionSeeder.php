<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Student permissions
            'can:view-own-grades',
            'can:submit-assignments',
            'can:view-own-gpa',
            'can:view-own-profile',
            'can:update-own-profile',

            // Lecturer permissions
            'can:manage-assignments',
            'can:grade-submissions',
            'can:upload-notes',
            'can:view-course-students',
            'can:manage-course-content',

            // Admin permissions
            'can:manage-semesters',
            'can:bulk-import-grades',
            'can:generate-reports',
            'can:manage-courses',
            'can:manage-enrollments',
            'can:view-all-students',
            'can:view-all-lecturers',

            // Accountant permissions
            'can:manage-payments',
            'can:set-fee-thresholds',
            'can:send-reminders',
            'can:view-payment-reports',
            'can:manage-student-fees',

            // Super Admin permissions
            'can:manage-all-roles',
            'can:system-settings',
            'can:manage-users',
            'can:view-system-logs',
            'can:backup-restore',
            'can:manage-permissions',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $studentRole = Role::create(['name' => 'student']);
        $studentRole->givePermissionTo([
            'can:view-own-grades',
            'can:submit-assignments',
            'can:view-own-gpa',
            'can:view-own-profile',
            'can:update-own-profile',
        ]);

        $lecturerRole = Role::create(['name' => 'lecturer']);
        $lecturerRole->givePermissionTo([
            'can:manage-assignments',
            'can:grade-submissions',
            'can:upload-notes',
            'can:view-course-students',
            'can:manage-course-content',
            'can:view-own-profile',
            'can:update-own-profile',
        ]);

        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo([
            'can:manage-semesters',
            'can:bulk-import-grades',
            'can:generate-reports',
            'can:manage-courses',
            'can:manage-enrollments',
            'can:view-all-students',
            'can:view-all-lecturers',
            'can:view-own-profile',
            'can:update-own-profile',
        ]);

        $accountantRole = Role::create(['name' => 'accountant']);
        $accountantRole->givePermissionTo([
            'can:manage-payments',
            'can:set-fee-thresholds',
            'can:send-reminders',
            'can:view-payment-reports',
            'can:manage-student-fees',
            'can:view-own-profile',
            'can:update-own-profile',
        ]);

        $superAdminRole = Role::create(['name' => 'super_admin']);
        $superAdminRole->givePermissionTo(Permission::all());
    }
}
