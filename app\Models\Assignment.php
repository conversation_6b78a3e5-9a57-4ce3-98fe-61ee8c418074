<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Assignment extends Model
{
    protected $fillable = [
        'course_id',
        'title',
        'description',
        'instructions',
        'max_points',
        'due_date',
        'available_from',
        'allow_late_submission',
        'late_penalty_percent',
        'attachment_path',
        'status',
        'created_by',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'available_from' => 'datetime',
        'allow_late_submission' => 'boolean',
        'max_points' => 'integer',
        'late_penalty_percent' => 'integer',
    ];

    /**
     * Get the course this assignment belongs to.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the lecturer who created this assignment.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the submissions for this assignment.
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(Submission::class);
    }

    /**
     * Scope to get only published assignments.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope to get available assignments (published and within availability window).
     */
    public function scopeAvailable($query)
    {
        $now = now();
        return $query->where('status', 'published')
            ->where(function ($q) use ($now) {
                $q->whereNull('available_from')
                    ->orWhere('available_from', '<=', $now);
            });
    }

    /**
     * Check if assignment is overdue.
     */
    public function isOverdue(): bool
    {
        return now() > $this->due_date;
    }

    /**
     * Check if assignment is available for submission.
     */
    public function isAvailable(): bool
    {
        $now = now();
        return $this->status === 'published' &&
            ($this->available_from === null || $this->available_from <= $now);
    }

    /**
     * Get submissions count.
     */
    public function getSubmissionsCountAttribute()
    {
        return $this->submissions()->count();
    }

    /**
     * Get graded submissions count.
     */
    public function getGradedSubmissionsCountAttribute()
    {
        return $this->submissions()->where('status', 'graded')->count();
    }
}
