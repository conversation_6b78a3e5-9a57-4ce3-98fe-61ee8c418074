<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Grade extends Model
{
    protected $fillable = [
        'enrollment_id',
        'grade_point',
        'letter_grade',
        'percentage',
        'comments',
        'graded_by',
        'graded_at',
    ];

    protected $casts = [
        'grade_point' => 'decimal:2',
        'percentage' => 'decimal:2',
        'graded_at' => 'datetime',
    ];

    /**
     * Get the enrollment this grade belongs to.
     */
    public function enrollment(): BelongsTo
    {
        return $this->belongsTo(Enrollment::class);
    }

    /**
     * Get the lecturer who graded this.
     */
    public function gradedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    /**
     * Convert percentage to grade point.
     */
    public static function percentageToGradePoint(float $percentage): array
    {
        if ($percentage >= 90) {
            return ['grade_point' => 4.00, 'letter_grade' => 'A+'];
        } elseif ($percentage >= 85) {
            return ['grade_point' => 3.75, 'letter_grade' => 'A'];
        } elseif ($percentage >= 80) {
            return ['grade_point' => 3.50, 'letter_grade' => 'A-'];
        } elseif ($percentage >= 75) {
            return ['grade_point' => 3.25, 'letter_grade' => 'B+'];
        } elseif ($percentage >= 70) {
            return ['grade_point' => 3.00, 'letter_grade' => 'B'];
        } elseif ($percentage >= 65) {
            return ['grade_point' => 2.75, 'letter_grade' => 'B-'];
        } elseif ($percentage >= 60) {
            return ['grade_point' => 2.50, 'letter_grade' => 'C+'];
        } elseif ($percentage >= 55) {
            return ['grade_point' => 2.25, 'letter_grade' => 'C'];
        } elseif ($percentage >= 50) {
            return ['grade_point' => 2.00, 'letter_grade' => 'C-'];
        } elseif ($percentage >= 45) {
            return ['grade_point' => 1.75, 'letter_grade' => 'D+'];
        } elseif ($percentage >= 40) {
            return ['grade_point' => 1.50, 'letter_grade' => 'D'];
        } else {
            return ['grade_point' => 0.00, 'letter_grade' => 'F'];
        }
    }

    /**
     * Calculate SGPA for a student in a specific semester.
     */
    public static function calculateSGPA(int $studentId, int $semesterId): float
    {
        $enrollments = Enrollment::where('user_id', $studentId)
            ->where('semester_id', $semesterId)
            ->where('status', 'completed')
            ->with(['grade', 'course'])
            ->get();

        $totalGradePoints = 0;
        $totalCreditHours = 0;

        foreach ($enrollments as $enrollment) {
            if ($enrollment->grade) {
                $gradePoint = $enrollment->grade->grade_point;
                $creditHours = $enrollment->course->credit_hours;

                $totalGradePoints += $gradePoint * $creditHours;
                $totalCreditHours += $creditHours;
            }
        }

        return $totalCreditHours > 0 ? round($totalGradePoints / $totalCreditHours, 2) : 0.00;
    }

    /**
     * Calculate CGPA for a student across all completed semesters.
     */
    public static function calculateCGPA(int $studentId): float
    {
        $enrollments = Enrollment::where('user_id', $studentId)
            ->where('status', 'completed')
            ->with(['grade', 'course'])
            ->get();

        $totalGradePoints = 0;
        $totalCreditHours = 0;

        foreach ($enrollments as $enrollment) {
            if ($enrollment->grade) {
                $gradePoint = $enrollment->grade->grade_point;
                $creditHours = $enrollment->course->credit_hours;

                $totalGradePoints += $gradePoint * $creditHours;
                $totalCreditHours += $creditHours;
            }
        }

        return $totalCreditHours > 0 ? round($totalGradePoints / $totalCreditHours, 2) : 0.00;
    }
}
