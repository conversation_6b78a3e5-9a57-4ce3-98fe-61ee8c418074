<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\FeeThreshold;
use App\Models\Grade;
use App\Models\Semester;
use App\Models\User;
use Illuminate\Database\Seeder;

class AcademicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create semesters
        $currentSemester = Semester::create([
            'name' => 'Fall 2024',
            'code' => 'FALL2024',
            'start_date' => '2024-09-01',
            'end_date' => '2024-12-15',
            'is_active' => true,
            'description' => 'Fall semester 2024',
        ]);

        $previousSemester = Semester::create([
            'name' => 'Spring 2024',
            'code' => 'SPRING2024',
            'start_date' => '2024-01-15',
            'end_date' => '2024-05-15',
            'is_active' => false,
            'description' => 'Spring semester 2024',
        ]);

        Semester::create([
            'name' => 'Spring 2025',
            'code' => 'SPRING2025',
            'start_date' => '2025-01-15',
            'end_date' => '2025-05-15',
            'is_active' => false,
            'description' => 'Spring semester 2025',
        ]);

        // Get users by role
        $lecturers = User::role('lecturer')->get();
        $students = User::role('student')->get();
        $accountant = User::role('accountant')->first();

        // Create additional students for testing
        for ($i = 2; $i <= 10; $i++) {
            $student = User::create([
                'name' => "Student User {$i}",
                'email' => "student{$i}@example.com",
                'password' => bcrypt('password'),
                'student_id' => sprintf('STU%03d', $i),
            ]);
            $student->assignRole('student');
            $students->push($student);
        }

        // Create additional lecturers
        for ($i = 2; $i <= 3; $i++) {
            $lecturer = User::create([
                'name' => "Lecturer User {$i}",
                'email' => "lecturer{$i}@example.com",
                'password' => bcrypt('password'),
            ]);
            $lecturer->assignRole('lecturer');
            $lecturers->push($lecturer);
        }

        // Create courses for current semester
        $courses = [
            [
                'code' => 'CS101',
                'name' => 'Introduction to Computer Science',
                'description' => 'Basic concepts of computer science and programming',
                'credit_hours' => 3,
                'lecturer_id' => $lecturers->first()->id,
            ],
            [
                'code' => 'MATH201',
                'name' => 'Calculus I',
                'description' => 'Differential and integral calculus',
                'credit_hours' => 4,
                'lecturer_id' => $lecturers->get(1)->id,
            ],
            [
                'code' => 'ENG101',
                'name' => 'English Composition',
                'description' => 'Academic writing and communication skills',
                'credit_hours' => 3,
                'lecturer_id' => $lecturers->get(2)->id,
            ],
            [
                'code' => 'PHYS101',
                'name' => 'General Physics I',
                'description' => 'Mechanics and thermodynamics',
                'credit_hours' => 4,
                'lecturer_id' => $lecturers->first()->id,
            ],
            [
                'code' => 'CHEM101',
                'name' => 'General Chemistry',
                'description' => 'Basic principles of chemistry',
                'credit_hours' => 3,
                'lecturer_id' => $lecturers->get(1)->id,
            ],
        ];

        $createdCourses = collect();
        foreach ($courses as $courseData) {
            $course = Course::create(array_merge($courseData, [
                'semester_id' => $currentSemester->id,
            ]));
            $createdCourses->push($course);
        }

        // Create courses for previous semester (for grade history)
        $previousCourses = [
            [
                'code' => 'CS100',
                'name' => 'Computer Fundamentals',
                'description' => 'Basic computer literacy and skills',
                'credit_hours' => 2,
                'lecturer_id' => $lecturers->first()->id,
            ],
            [
                'code' => 'MATH101',
                'name' => 'College Algebra',
                'description' => 'Algebraic concepts and problem solving',
                'credit_hours' => 3,
                'lecturer_id' => $lecturers->get(1)->id,
            ],
        ];

        $previousCreatedCourses = collect();
        foreach ($previousCourses as $courseData) {
            $course = Course::create(array_merge($courseData, [
                'semester_id' => $previousSemester->id,
            ]));
            $previousCreatedCourses->push($course);
        }

        // Create fee thresholds
        FeeThreshold::create([
            'name' => 'Grade Access Threshold',
            'amount' => 500.00,
            'description' => 'Students with outstanding balance above this amount cannot access grades',
            'type' => 'grade_access',
            'is_active' => true,
            'created_by' => $accountant->id,
        ]);

        FeeThreshold::create([
            'name' => 'Registration Threshold',
            'amount' => 1000.00,
            'description' => 'Students with outstanding balance above this amount cannot register for new courses',
            'type' => 'registration',
            'is_active' => true,
            'created_by' => $accountant->id,
        ]);

        // Enroll students in current semester courses
        foreach ($students->take(8) as $student) {
            // Each student enrolls in 3-4 courses
            $coursesToEnroll = $createdCourses->random(rand(3, 4));

            foreach ($coursesToEnroll as $course) {
                Enrollment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'semester_id' => $currentSemester->id,
                    'status' => 'enrolled',
                    'enrolled_at' => $currentSemester->start_date,
                ]);
            }
        }

        // Create completed enrollments for previous semester with grades
        foreach ($students->take(6) as $student) {
            foreach ($previousCreatedCourses as $course) {
                $enrollment = Enrollment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'semester_id' => $previousSemester->id,
                    'status' => 'completed',
                    'enrolled_at' => $previousSemester->start_date,
                ]);

                // Create grades for completed enrollments
                $percentage = rand(60, 95);
                $gradeData = Grade::percentageToGradePoint($percentage);

                Grade::create([
                    'enrollment_id' => $enrollment->id,
                    'grade_point' => $gradeData['grade_point'],
                    'letter_grade' => $gradeData['letter_grade'],
                    'percentage' => $percentage,
                    'comments' => 'Good work this semester',
                    'graded_by' => $course->lecturer_id,
                    'graded_at' => $previousSemester->end_date,
                ]);
            }
        }

        $this->command->info('Academic data seeded successfully!');
    }
}
