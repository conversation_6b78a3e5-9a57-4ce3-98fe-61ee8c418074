<?php

namespace App\Services;

use App\Models\Enrollment;
use App\Models\FeeThreshold;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\User;

class GradeService
{
    /**
     * Check if a student can access their grades based on fee threshold.
     */
    public function canStudentAccessGrades(int $studentId, int $semesterId): bool
    {
        $student = User::find($studentId);
        
        if (!$student || !$student->isStudent()) {
            return false;
        }

        return !FeeThreshold::studentExceedsGradeAccessThreshold($studentId, $semesterId);
    }

    /**
     * Get student's grades for a specific semester with fee threshold check.
     */
    public function getStudentGrades(int $studentId, int $semesterId): array
    {
        if (!$this->canStudentAccessGrades($studentId, $semesterId)) {
            $threshold = FeeThreshold::getGradeAccessThreshold();
            $outstandingBalance = Payment::getOutstandingBalance($studentId, $semesterId);
            
            return [
                'success' => false,
                'message' => "Access denied. Outstanding balance of $" . number_format($outstandingBalance, 2) . 
                           " exceeds the threshold of $" . number_format($threshold, 2) . ". Please clear your dues to access grades.",
                'grades' => [],
                'sgpa' => null,
                'cgpa' => null,
            ];
        }

        $enrollments = Enrollment::where('user_id', $studentId)
            ->where('semester_id', $semesterId)
            ->with(['course', 'grade'])
            ->get();

        $grades = $enrollments->map(function ($enrollment) {
            return [
                'course_code' => $enrollment->course->code,
                'course_name' => $enrollment->course->name,
                'credit_hours' => $enrollment->course->credit_hours,
                'grade_point' => $enrollment->grade?->grade_point,
                'letter_grade' => $enrollment->grade?->letter_grade,
                'percentage' => $enrollment->grade?->percentage,
                'status' => $enrollment->status,
            ];
        });

        $sgpa = Grade::calculateSGPA($studentId, $semesterId);
        $cgpa = Grade::calculateCGPA($studentId);

        return [
            'success' => true,
            'message' => 'Grades retrieved successfully.',
            'grades' => $grades,
            'sgpa' => $sgpa,
            'cgpa' => $cgpa,
        ];
    }

    /**
     * Calculate and update GPA for a student after grade changes.
     */
    public function updateStudentGPA(int $studentId): array
    {
        $student = User::find($studentId);
        
        if (!$student) {
            return ['success' => false, 'message' => 'Student not found.'];
        }

        // Get all semesters the student has completed courses in
        $semesters = Enrollment::where('user_id', $studentId)
            ->where('status', 'completed')
            ->with('semester')
            ->get()
            ->pluck('semester')
            ->unique('id');

        $semesterGPAs = [];
        foreach ($semesters as $semester) {
            $sgpa = Grade::calculateSGPA($studentId, $semester->id);
            $semesterGPAs[] = [
                'semester_id' => $semester->id,
                'semester_name' => $semester->name,
                'sgpa' => $sgpa,
            ];
        }

        $cgpa = Grade::calculateCGPA($studentId);

        return [
            'success' => true,
            'message' => 'GPA calculated successfully.',
            'semester_gpas' => $semesterGPAs,
            'cgpa' => $cgpa,
        ];
    }

    /**
     * Get grade statistics for a course.
     */
    public function getCourseGradeStatistics(int $courseId): array
    {
        $enrollments = Enrollment::where('course_id', $courseId)
            ->where('status', 'completed')
            ->with('grade')
            ->get();

        if ($enrollments->isEmpty()) {
            return [
                'total_students' => 0,
                'graded_students' => 0,
                'average_gpa' => 0,
                'grade_distribution' => [],
            ];
        }

        $gradedEnrollments = $enrollments->filter(fn($e) => $e->grade !== null);
        $gradePoints = $gradedEnrollments->pluck('grade.grade_point');
        
        $averageGPA = $gradePoints->avg();
        
        // Grade distribution
        $gradeDistribution = $gradedEnrollments
            ->groupBy('grade.letter_grade')
            ->map(fn($group) => $group->count())
            ->toArray();

        return [
            'total_students' => $enrollments->count(),
            'graded_students' => $gradedEnrollments->count(),
            'average_gpa' => round($averageGPA, 2),
            'grade_distribution' => $gradeDistribution,
        ];
    }

    /**
     * Bulk import grades from array data.
     */
    public function bulkImportGrades(array $gradesData, int $lecturerId): array
    {
        $imported = 0;
        $errors = [];

        foreach ($gradesData as $index => $gradeData) {
            try {
                $enrollment = Enrollment::where('user_id', $gradeData['student_id'])
                    ->where('course_id', $gradeData['course_id'])
                    ->first();

                if (!$enrollment) {
                    $errors[] = "Row {$index}: Enrollment not found for student ID {$gradeData['student_id']} in course ID {$gradeData['course_id']}";
                    continue;
                }

                // Convert percentage to grade point if percentage is provided
                if (isset($gradeData['percentage'])) {
                    $gradeInfo = Grade::percentageToGradePoint($gradeData['percentage']);
                    $gradeData['grade_point'] = $gradeInfo['grade_point'];
                    $gradeData['letter_grade'] = $gradeInfo['letter_grade'];
                }

                Grade::updateOrCreate(
                    ['enrollment_id' => $enrollment->id],
                    [
                        'grade_point' => $gradeData['grade_point'],
                        'letter_grade' => $gradeData['letter_grade'],
                        'percentage' => $gradeData['percentage'] ?? null,
                        'comments' => $gradeData['comments'] ?? null,
                        'graded_by' => $lecturerId,
                        'graded_at' => now(),
                    ]
                );

                // Update enrollment status to completed
                $enrollment->update(['status' => 'completed']);
                
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row {$index}: " . $e->getMessage();
            }
        }

        return [
            'success' => $imported > 0,
            'imported' => $imported,
            'errors' => $errors,
            'message' => "Successfully imported {$imported} grades" . (count($errors) > 0 ? " with " . count($errors) . " errors." : "."),
        ];
    }
}
