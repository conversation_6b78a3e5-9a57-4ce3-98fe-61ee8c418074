<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fee_thresholds', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Grade Access Threshold", "Registration Threshold"
            $table->decimal('amount', 10, 2); // threshold amount
            $table->text('description')->nullable();
            $table->enum('type', ['grade_access', 'registration', 'general'])->default('general');
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // accountant/admin
            $table->timestamps();

            $table->index(['type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fee_thresholds');
    }
};
