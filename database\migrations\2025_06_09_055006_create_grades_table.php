<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('enrollment_id')->constrained()->onDelete('cascade');
            $table->decimal('grade_point', 3, 2); // e.g., 4.00, 3.75, 2.50
            $table->string('letter_grade', 2); // e.g., "A+", "B", "C-", "F"
            $table->decimal('percentage', 5, 2)->nullable(); // e.g., 95.50
            $table->text('comments')->nullable();
            $table->foreignId('graded_by')->nullable()->constrained('users')->onDelete('set null'); // lecturer who graded
            $table->timestamp('graded_at')->nullable();
            $table->timestamps();

            $table->unique('enrollment_id'); // One grade per enrollment
            $table->index(['grade_point', 'letter_grade']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grades');
    }
};
